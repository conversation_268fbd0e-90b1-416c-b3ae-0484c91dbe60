'use client'

import { Table } from '@tanstack/react-table'

import Filter from '@/components/filter'

interface DataTableToolbarProps<TData> {
    table: Table<TData>
    onChange: any
    /** Optional onClick handler for report generation or filter application */
    onClick?: () => void
    /** Optional additional props to pass to the Filter component */
    filterProps?: Record<string, any>
}

export function DataTableToolbar<TData>({
    table,
    onChange,
    onClick,
    filterProps = {},
}: DataTableToolbarProps<TData>) {
    return (
        <Filter
            onChange={onChange}
            onClick={onClick}
            table={table}
            {...filterProps}
        />
    )
}
