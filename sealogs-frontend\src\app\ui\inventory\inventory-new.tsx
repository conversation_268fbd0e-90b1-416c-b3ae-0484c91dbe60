'use client'
import React, { useState } from 'react'
import { useMutation } from '@apollo/client'
import Link from 'next/link'
import Editor from '@/app/ui/editor'
import {
    CREATE_INVENTORY,
    CREATE_INVENTORY_CATEGORY,
    CREATE_SEALOGS_FILE_LINKS,
    CREATE_SUPPLIER,
} from '@/app/lib/graphQL/mutation'
import { useRouter, useSearchParams } from 'next/navigation'
import { InputSkeleton } from '@/components/skeletons'
import {
    getVesselList,
    getSupplier,
    getInventoryCategory,
} from '@/app/lib/actions'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Combobox } from '@/components/ui/comboBox'
import { FooterWrapper } from '@/components/footer-wrapper'
import {
    AlertDialogNew,
    <PERSON><PERSON>,
    <PERSON>,
    H<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
} from '@/components/ui'
import { SealogsInventoryIcon } from '@/app/lib/icons/SealogsInventoryIcon'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import CloudFlareCaptures from '../logbook/components/CloudFlareCaptures'

export default function NewInventory({ vesselID = 0 }: { vesselID: number }) {
    const searchParams = useSearchParams()
    const [categories, setCategories] = useState<any>()
    const [selectedCategories, setSelectedCategories] = useState<any>()
    const [suppliers, setSuppliers] = useState<any>()
    const [selectedSuppliers, setSelectedSuppliers] = useState<any>()
    const [isMounted, setIsMounted] = useState(false)
    const [vessels, setVessels] = useState<any>()
    const [location, setLocations] = useState<any>()
    const [selectedVessel, setSelectedVessel] = useState<any>()
    const [selectedLocation, setSelectedLocation] = useState<any>()
    const { getVesselWithIcon } = useVesselIconData()
    const [openLocationDialog, setOpenLocationDialog] = useState(false)
    const [openSupplierDialog, setOpenSupplierDialog] = useState(false)
    const [openCategoryDialog, setOpenCategoryDialog] = useState(false)
    const [documents, setDocuments] = useState<Array<Record<string, any>>>([])
    const [fileLinks, setFileLinks] = useState<any>([])
    const [linkSelectedOption, setLinkSelectedOption] = useState<any>([])
    const router = useRouter()

    var description = ''

    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const vesselList = activeVessels.map((item: any) => ({
            ...item,
        }))
        const appendedData = [
            // { title: '-- Other --', id: 'newLocation' },
            ...vesselList,
            { title: 'Other', id: '0' },
        ]
        setVessels(appendedData)
        if (vesselID > 0) {
            const vessel = activeVessels.find(
                (vessel: any) => vessel.id === vesselID,
            )
            if (vessel) {
                const vesselWithIcon = getVesselWithIcon(vessel.id, vessel)
                setSelectedLocation({
                    label: vessel.title,
                    value: vesselID,
                    vessel: vesselWithIcon,
                })
            }
        }
    }

    getVesselList(handleSetVessels)

    const handelSetSuppliers = (data: any) => {
        const suppliersList = [
            {
                label: ' ---- Create supplier ---- ',
                value: 'newSupplier',
            },
            ...data
                ?.filter((supplier: any) => supplier.name !== null)
                .map((supplier: any) => ({
                    label: supplier.name,
                    value: supplier.id,
                })),
        ]
        setSuppliers(suppliersList)
    }

    getSupplier(handelSetSuppliers)

    const handleSetCategories = (data: any) => {
        const categoriesList = [
            {
                label: ' ---- Create Category ---- ',
                value: 'newCategory',
            },
            ...data
                ?.filter((category: any) => category.name !== null)
                .map((category: any) => ({
                    label: category.name,
                    value: category.id,
                })),
        ]
        setCategories(categoriesList)
    }

    getInventoryCategory(handleSetCategories)

    const handleSetSelectedCategories = (selectedOption: any) => {
        if (
            selectedOption.find((option: any) => option.value === 'newCategory')
        ) {
            setOpenCategoryDialog(true)
        }
        setSelectedCategories(
            selectedOption.filter(
                (option: any) => option.value !== 'newCategory',
            ),
        )
    }

    const handleEditorChange = (desc: any) => {
        description = desc
    }

    const handleCreate = async () => {
        const variables = {
            input: {
                item: (
                    document.getElementById(
                        'inventory-name',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-name',
                          ) as HTMLInputElement
                      ).value
                    : null,
                description: null,
                content: description,
                quantity: (
                    document.getElementById('inventory-qty') as HTMLInputElement
                ).value
                    ? parseInt(
                          (
                              document.getElementById(
                                  'inventory-qty',
                              ) as HTMLInputElement
                          ).value,
                      )
                    : null,
                productCode: (
                    document.getElementById(
                        'inventory-code',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-code',
                          ) as HTMLInputElement
                      ).value
                    : null,
                costingDetails: (
                    document.getElementById(
                        'inventory-cost',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-cost',
                          ) as HTMLInputElement
                      ).value
                    : null,
                documents: documents.map((doc: any) => doc.id).join(','),
                categories: selectedCategories?.map(
                    (category: any) => category.value,
                ).length
                    ? selectedCategories
                          .map((category: any) => category.value)
                          .join(',')
                    : null,
                suppliers: selectedSuppliers?.map(
                    (supplier: any) => supplier.value,
                ).length
                    ? selectedSuppliers
                          .map((supplier: any) => supplier.value)
                          .join(',')
                    : null,
                location: (
                    document.getElementById(
                        'inventory-location',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-location',
                          ) as HTMLInputElement
                      ).value
                    : null,
                vesselID: vesselID > 0 ? vesselID : selectedLocation?.value,
                attachmentLinks: linkSelectedOption
                    ? linkSelectedOption
                          .map((link: any) => link.value)
                          .join(',')
                    : '',
            },
        }
        await mutationCreateInventory({
            variables,
        })
    }

    const [
        mutationCreateInventory,
        { loading: mutationcreateInventoryLoading },
    ] = useMutation(CREATE_INVENTORY, {
        onCompleted: (response: any) => {
            const data = response.createInventory
            if (data.id > 0) {
                searchParams.get('redirect_to')
                    ? router.push(searchParams?.get('redirect_to') + '')
                    : router.back()
            } else {
                console.error('mutationcreateInventory error', response)
            }
        },
        onError: (error: any) => {
            console.error('mutationcreateInventory error', error)
        },
    })

    const handleCreateCategory = async () => {
        const categoryName = (
            document.getElementById(
                'inventory-new-category',
            ) as HTMLInputElement
        ).value
        return await mutationcreateInventoryCategory({
            variables: {
                input: {
                    name: categoryName,
                },
            },
        })
    }

    const [
        mutationcreateInventoryCategory,
        { loading: mutationcreateInventoryCategoryLoading },
    ] = useMutation(CREATE_INVENTORY_CATEGORY, {
        onCompleted: (response: any) => {
            const data = response.createInventoryCategory
            if (data.id > 0) {
                const categoriesList = [
                    ...categories,
                    { label: data.name, value: data.id },
                ]
                setCategories(categoriesList)
                const selectedCategoriesList = [
                    ...selectedCategories,
                    { label: data.name, value: data.id },
                ]
                setSelectedCategories(selectedCategoriesList)
                setOpenCategoryDialog(false)
            } else {
                console.error('mutationcreateInventoryCategory error', response)
            }
        },
        onError: (error: any) => {
            console.error('mutationcreateInventoryCategory error', error)
        },
    })

    const handleSelectedVesselChange = (selectedOption: any) => {
        if (selectedOption.value === 'newLocation') {
            setOpenLocationDialog(true)
        }
        setSelectedLocation(selectedOption)
    }

    const handleCreateLocation = (Location: any) => {
        var newLocation = { label: '', value: '' }
        if (typeof Location === 'string') {
            newLocation = { label: Location, value: Location }
        }
        if (typeof Location === 'object') {
            newLocation = {
                label: (
                    document.getElementById(
                        'inventory-new-location',
                    ) as HTMLInputElement
                ).value,
                value: (
                    document.getElementById(
                        'inventory-new-location-id',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-new-location-id',
                          ) as HTMLInputElement
                      ).value
                    : (
                          document.getElementById(
                              'inventory-new-location',
                          ) as HTMLInputElement
                      ).value,
            }
        }
        const vesselList = vessels.map((item: any) => ({
            ...item,
        }))
        const appendedData = [
            ...vesselList,
            { Title: newLocation.label, ID: newLocation.value },
        ]
        setVessels(appendedData)
        setSelectedLocation(newLocation)
        setOpenLocationDialog(false)
    }

    const handleSelectedSuppliers = (selectedOption: any) => {
        if (
            selectedOption.find((option: any) => option.value === 'newSupplier')
        ) {
            setOpenSupplierDialog(true)
        }
        setSelectedSuppliers(
            selectedOption.filter(
                (option: any) => option.value !== 'newSupplier',
            ),
        )
    }

    const handleCreateSupplier = async () => {
        const name = (
            document.getElementById('supplier-name') as HTMLInputElement
        ).value
        const website = (
            document.getElementById('supplier-website') as HTMLInputElement
        ).value
        const phone = (
            document.getElementById('supplier-phone') as HTMLInputElement
        ).value
        const email = (
            document.getElementById('supplier-email') as HTMLInputElement
        ).value
        const address = (
            document.getElementById('supplier-address') as HTMLInputElement
        ).value

        const variables = {
            input: {
                name: name,
                address: address,
                website: website,
                email: email,
                phone: phone,
            },
        }
        if (name !== '') {
            await mutationCreateSupplier({
                variables,
            })
        }
        setOpenSupplierDialog(false)
    }

    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] =
        useMutation(CREATE_SUPPLIER, {
            onCompleted: (response: any) => {
                const data = response.createSupplier
                if (data.id > 0) {
                    const suppliersList = [
                        ...suppliers,
                        { label: data.name, value: data.id },
                    ]
                    setSuppliers(suppliersList)
                    const selectedSuppliersList = [
                        ...selectedSuppliers,
                        { label: data.name, value: data.id },
                    ]
                    setSelectedSuppliers(selectedSuppliersList)
                } else {
                    console.error('mutationcreateSupplier error', response)
                }
            },
            onError: (error: any) => {
                console.error('mutationcreateSupplier error', error)
            },
        })

    const [createSeaLogsFileLinks] = useMutation(CREATE_SEALOGS_FILE_LINKS, {
        onCompleted: (response: any) => {
            const data = response.createSeaLogsFileLinks
            if (data.id > 0) {
                const newLinks = [...fileLinks, data]
                setFileLinks(newLinks)
                linkSelectedOption
                    ? setLinkSelectedOption([
                          ...linkSelectedOption,
                          { label: data.link, value: data.id },
                      ])
                    : setLinkSelectedOption([
                          { label: data.link, value: data.id },
                      ])
            }
        },
        onError: (error: any) => {
            console.error('createSeaLogsFileLinksEntry error', error)
        },
    })

    const handleDeleteLink = (link: any) => {
        setLinkSelectedOption(linkSelectedOption.filter((l: any) => l !== link))
    }

    const linkItem = (link: any) => {
        if (!link.label) {
            return null
        }
        return (
            <div className="flex justify-between align-middle mr-2 w-fit">
                <Link href={link.label} target="_blank" className="ml-2 ">
                    {link.label}
                </Link>
                <div className="ml-2 ">
                    <Button
                        iconLeft="cross_icon"
                        onClick={() => handleDeleteLink(link)}
                    />
                </div>
            </div>
        )
    }

    return (
        <>
            <ListHeader
                icon={
                    <SealogsInventoryIcon
                        className={`h-12 w-12 ring-1 p-1 rounded-full bg-background`}
                    />
                }
                title="New inventory item"
            />

            <Card className="space-y-8 px-2">
                {/* Basic Information Section */}
                {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> */}
                <div className="space-y-4">
                    <div className="grid sm:grid-cols-2 gap-4">
                        <Label label="Inventory name" htmlFor="inventory-name">
                            <Input
                                id="inventory-name"
                                type="text"
                                placeholder="Inventory name"
                                className="w-full"
                            />
                        </Label>

                        <Label label="Vessel" htmlFor="inventory-vessel">
                            {vessels ? (
                                <Combobox
                                    id="inventory-vessel"
                                    options={vessels?.map((vessel: any) => {
                                        const vesselWithIcon =
                                            getVesselWithIcon(vessel.id, vessel)
                                        return {
                                            label: vessel.title,
                                            value: vessel.id,
                                            vessel: vesselWithIcon,
                                        }
                                    })}
                                    defaultValues={selectedLocation}
                                    placeholder="Select Vessel"
                                    onChange={handleSelectedVesselChange}
                                />
                            ) : (
                                <InputSkeleton />
                            )}
                        </Label>
                    </div>
                    <div className="grid sm:grid-cols-2 gap-4">
                        <Label label="Location" htmlFor="inventory-location">
                            <Input
                                id="inventory-location"
                                type="text"
                                placeholder="Location"
                                className="w-full"
                            />
                        </Label>

                        <Label label="Quantity" htmlFor="inventory-qty">
                            <Input
                                id="inventory-qty"
                                type="number"
                                placeholder="Quantity"
                                className="w-full"
                            />
                        </Label>
                    </div>
                </div>

                {/* Description Section */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                        <H5>Description</H5>
                        <P>
                            Enter details that might help with the maintenance
                            or operation of this item.
                        </P>
                    </div>

                    <div className="col-span-2">
                        <Editor
                            id="inventory-Content"
                            handleEditorChange={handleEditorChange}
                        />
                    </div>
                </div>
                {/* <Label
                        label="Short Description"
                        htmlFor="inventory-short-description"
                        className="col-span-2">
                        <Textarea
                            id="inventory-short-description"
                            rows={12}
                            className="w-full resize-none"
                            placeholder="Short description"
                        />
                    </Label> */}
                {/* </div> */}

                <Separator />

                {/* Inventory Details Section */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                        <H5>Inventory details</H5>
                        <P>
                            In this section categorise the item and add the
                            suppliers where you normally purchase this item and
                            the expected cost. This will help replacing the item
                            in the future.
                        </P>
                    </div>

                    <div className="col-span-2 space-y-5">
                        <Label
                            label="Product code"
                            htmlFor="inventory-code"
                            className="grid grid-cols-1 md:grid-cols-[120px_1fr] items-center">
                            <Input
                                id="inventory-code"
                                type="text"
                                placeholder="Product code"
                                className="w-full"
                            />
                        </Label>

                        <Label
                            label="Categories"
                            htmlFor="inventory-categories"
                            className="grid grid-cols-1 md:grid-cols-[120px_1fr] items-center">
                            {categories ? (
                                <Combobox
                                    id="inventory-categories"
                                    multi
                                    options={categories}
                                    value={selectedCategories}
                                    onChange={handleSetSelectedCategories}
                                />
                            ) : (
                                <InputSkeleton />
                            )}
                        </Label>

                        <Label
                            label="Supplier"
                            htmlFor="inventory-suppliers"
                            className="grid grid-cols-1 md:grid-cols-[120px_1fr] items-center">
                            {suppliers ? (
                                <Combobox
                                    id="inventory-suppliers"
                                    multi
                                    value={selectedSuppliers}
                                    onChange={handleSelectedSuppliers}
                                    options={suppliers}
                                />
                            ) : (
                                <InputSkeleton />
                            )}
                        </Label>

                        <Label
                            label="Cost"
                            htmlFor="inventory-cost"
                            className="grid grid-cols-1 md:grid-cols-[120px_1fr] items-center">
                            <Input
                                id="inventory-cost"
                                type="text"
                                placeholder="Costing details"
                                className="w-full"
                            />
                        </Label>
                    </div>
                </div>

                <Separator />

                {/* Attachments Section */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                        <H5>Attachments</H5>
                        <P>
                            Upload things like photos of the item, plus warranty
                            and guarantee documents or operating manuals. Add
                            links to any online manuals or product descriptions.
                        </P>
                    </div>

                    <div className="col-span-2 space-y-5">
                        {/* <div className="w-full"> */}
                        {/* <FileUpload
                                setDocuments={setDocuments}
                                text=""
                                subText="Drag files here or upload"
                                documents={documents}
                            /> */}
                        {/* </div> */}
                        <div className="w-full flex flex-col space-y-2">
                            <CloudFlareCaptures
                                inputId={0}
                                sectionId={0}
                                buttonType={'button'}
                                sectionName={'inventoryID'}
                            />
                        </div>

                        <Label label="Links" htmlFor="inventory-links">
                            <Input
                                id="inventory-links"
                                type="text"
                                placeholder="Links to manuals or product descriptions"
                                className="w-full"
                                onKeyDown={async (
                                    event: React.KeyboardEvent<HTMLInputElement>,
                                ) => {
                                    if (event.key === 'Enter') {
                                        const inputValue = (
                                            event.target as HTMLInputElement
                                        ).value
                                        await createSeaLogsFileLinks({
                                            variables: {
                                                input: {
                                                    link: inputValue,
                                                },
                                            },
                                        })
                                        ;(
                                            event.target as HTMLInputElement
                                        ).value = ''
                                    }
                                }}
                            />
                        </Label>
                        <div className="flex flex-wrap gap-2">
                            {linkSelectedOption
                                ? linkSelectedOption.map((link: any) => (
                                      <div key={link.value}>
                                          {linkItem(link)}
                                      </div>
                                  ))
                                : fileLinks.map((link: any) => (
                                      <div key={link.value}>
                                          {linkItem(link)}
                                      </div>
                                  ))}
                        </div>
                    </div>
                </div>
            </Card>

            <FooterWrapper>
                <Button
                    size="sm"
                    variant="back"
                    onClick={() => router.push('/inventory')}>
                    Cancel
                </Button>
                <Button size="sm" onClick={handleCreate}>Create inventory</Button>
            </FooterWrapper>

            {/* Alert Dialogs - Keeping these untouched as requested */}
            <AlertDialogNew
                openDialog={openLocationDialog}
                setOpenDialog={setOpenLocationDialog}
                handleCreate={() => handleCreateLocation({})}
                title="Create New Location"
                actionText="Create Location">
                <Label
                    label="Location"
                    htmlFor="inventory-new-location"
                    className="my-4">
                    <Input
                        id={`inventory-new-location`}
                        type="text"
                        placeholder="Location"
                    />
                </Label>
                <Label label="Location ID" htmlFor="inventory-new-location-id">
                    <Input
                        id={`inventory-new-location-id`}
                        type="text"
                        placeholder="Location ID"
                    />
                </Label>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openSupplierDialog}
                setOpenDialog={setOpenSupplierDialog}
                handleCreate={handleCreateSupplier}
                actionText="Create supplier"
                className="lg:max-w-lg"
                title="Create new supplier">
                <div className="mt-4 space-y-4">
                    <Label label="Supplier name" htmlFor="supplier-name">
                        <Input
                            id={`supplier-name`}
                            type="text"
                            placeholder="Supplier name"
                        />
                    </Label>
                    <Label label="Website" htmlFor="supplier-website">
                        <Input
                            id={`supplier-website`}
                            type="text"
                            placeholder="Website"
                        />
                    </Label>
                    <Label label="Phone" htmlFor="supplier-phone">
                        <Input
                            id={`supplier-phone`}
                            type="text"
                            placeholder="Phone"
                        />
                    </Label>
                    <Label label="Email" htmlFor="supplier-email">
                        <Input
                            id={`supplier-email`}
                            type="email"
                            placeholder="Email"
                        />
                    </Label>
                    <Label label="Address" htmlFor="supplier-address">
                        <Textarea
                            id={`supplier-address`}
                            rows={4}
                            placeholder="Supplier address"
                        />
                    </Label>
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openCategoryDialog}
                setOpenDialog={setOpenCategoryDialog}
                handleCreate={handleCreateCategory}
                actionText="Create Category"
                title="Create new category">
                <Label
                    label="Category"
                    htmlFor="inventory-new-category"
                    className="my-4">
                    <Input
                        id={`inventory-new-category`}
                        type="text"
                        placeholder="Category"
                    />
                </Label>
            </AlertDialogNew>
        </>
    )
}
