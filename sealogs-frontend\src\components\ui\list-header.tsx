'use client'

import React from 'react'
import { H1 } from '@/components/ui/typography'

interface ListHeaderProps {
    /** The icon component to display */
    icon?: React.ReactNode
    /** The title text to display */
    title: string
    /** Optional action component (e.g., filter actions) */
    actions?: React.ReactNode
    /** Additional CSS classes for the icon container */
    iconClassName?: string
    /** Additional CSS classes for the title */
    titleClassName?: string
    className?: string
}

/**
 * Reusable header component for list pages with consistent styling
 * Used across crew, vessels, maintenance, and other list components
 */
export function ListHeader({
    icon,
    title,
    actions,
    iconClassName = '',
    titleClassName = '',
    className ='',
}: ListHeaderProps) {
    return (
        <div className={`${className} bg-card phablet:bg-background pb-1.5 xs:pb-[7px] z-50 sticky top-0 left-0 right-0 pt-3 xs:px-2`}>
            <div className="flex items-start justify-between gap-4 flex-nowrap">
                <div className="flex py-3 min-w-0 flex-1 gap-x-4">
                    {icon && (
                        <div
                            className={`flex-shrink-0 hidden xs:block ${iconClassName}`}>
                            {icon}
                        </div>
                    )}
                    <div className="flex items-center pe-4">
                        <H1 className={`${titleClassName} text-wrap`}>
                            {title}
                        </H1>
                    </div>
                </div>
                {actions && (
                    <div className="flex-shrink-0 py-3">
                        {actions}
                    </div>
                )}
            </div>
        </div>
    )
}
