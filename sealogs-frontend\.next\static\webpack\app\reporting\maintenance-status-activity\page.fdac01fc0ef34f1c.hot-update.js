"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/maintenance-status-activity/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _export_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./export-button */ \"(app-pages-browser)/./src/app/ui/reporting/export-button.tsx\");\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst tableHeadings = [\n    \"Task Name\",\n    \"Inventory\",\n    \"Location\",\n    \"Assigned To\",\n    \"Status\",\n    \"Due Date\",\n    \"Due Status\"\n];\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_4__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_6___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_6___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_6___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                dueStatusLabel(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_9__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_6___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                dueStatusLabel(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_8__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                    variant: \"back\",\n                    iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    onClick: ()=>router.push(\"/reporting\"),\n                    children: \"Back\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 264,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Card, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardContent, {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onChange: handleFilterOnChange,\n                            onClick: generateReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_export_button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onDownloadPdf: downloadPdf,\n                            onDownloadCsv: downloadCsv\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                        children: tableHeadings.map((heading)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                children: heading\n                                            }, heading, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                    isLoading: called && loading,\n                                    reportData: reportData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 275,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"oU/hWC7Aj40C5AHa8r/sGIGstSo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                    colSpan: tableHeadings.length,\n                    className: \"text-center  h-32\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 316,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n            lineNumber: 315,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                    colSpan: tableHeadings.length,\n                    className: \"text-center  h-32\",\n                    children: \"No Data Available\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 330,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n            lineNumber: 329,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n        children: reportData.map((element, index)=>{\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                className: \"group border-b  hover: \",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                        className: \"px-2 py-3 text-left w-[15%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" inline-block ml-3\",\n                            children: element.taskName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                        className: \"px-2 py-3 text-left w-[10%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" inline-block \",\n                            children: element.inventoryName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                        className: \"px-2 py-3 text-left w-[10%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" inline-block \",\n                            children: element.vesselName\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                        className: \"px-2 py-3 text-left w-[10%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" inline-block \",\n                            children: element.assignedTo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                        className: \"px-2 py-3 text-left w-[10%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" inline-block \",\n                            children: element.status\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                        className: \"px-2 py-3 text-left w-[10%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" inline-block \",\n                            children: element.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_6___default()(element.dueDate).format(\"DD/MM/YY\") : \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                        className: \"px-2 py-3 text-left w-[10%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" inline-block \",\n                            children: dueStatusLabel(element.dueStatus)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, \"report-item-\".concat(index), true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 345,\n                columnNumber: 21\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n        lineNumber: 342,\n        columnNumber: 9\n    }, this);\n}\n_c1 = TableContent;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_7___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_7___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c, _c1;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});