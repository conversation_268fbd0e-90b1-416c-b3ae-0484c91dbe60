'use client'

import { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import { debounce, isEmpty, trim } from 'lodash'
import { CREATE_USER, UPDATE_USER } from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import {
    getSeaLogsMembers,
    getVesselList,
    getSeaLogsGroups,
    getDepartmentList,
} from '@/app/lib/actions'
import CrewDutyDropdown from '@/components/filter/components/crew-duty-dropdown'
import DepartmentMultiSelectDropdown from '../department/multiselect-dropdown'

import { toast } from 'sonner'
import { isAdmin } from '@/app/helpers/userHelper'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { READ_ONE_SEALOGS_MEMBER } from '@/app/lib/graphQL/query'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { FooterWrapper } from '@/components/footer-wrapper'
import { AlertDialogNew, H3, H4, ListHeader, P } from '@/components/ui'
import { useResponsiveLabel } from '../../../../utils/responsiveLabel'

const UserForm = ({ userId }: { userId: number }) => {
    const [isUserAdmin, setIsUserAdmin] = useState<any>(-1)
    const router = useRouter()

    const [user, setUser] = useState<any>()
    const [hasFormErrors, setHasFormErrors] = useState(false)
    const [formErrors, setFormErrors] = useState({
        firstName: '',
        email: '',
        response: '',
    })
    const [userGroups, setUserGroups] = useState([])
    const [userVessels, setUserVessels] = useState([])
    const [groups, setGroups] = useState<any>()
    const [vessels, setVessels] = useState<any>()
    const [departmentList, setDepartmentList] = useState<any>(false)
    const [selectedDepartments, setSelectedDepartments] = useState([] as any[])
    const [permissions, setPermissions] = useState<any>(false)
    const [isSelf, setIsSelf] = useState(false)
    const [currentDepartment, setCurrentDepartment] = useState<any>(false)
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const [companyDetail, setCompanyDetail] = useState<any>(null)
    const [openArchiveDialog, setOpenArchiveDialog] = useState(false)

    const responsiveLabel = useResponsiveLabel()

    const [querySeaLogsMembers] = useLazyQuery(READ_ONE_SEALOGS_MEMBER, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneSeaLogsMember
            if (data) {
                setCurrentDepartment(data.departments.nodes)
            }
        },
        onError: (error: any) => {
            console.error('querySeaLogsMembers error', error)
        },
    })

    useEffect(() => {
        querySeaLogsMembers({
            variables: {
                filter: { id: { eq: +(localStorage.getItem('userId') ?? 0) } },
            },
        })
    }, [])

    useEffect(() => {
        setPermissions(getPermissions)
        setIsUserAdmin(isAdmin())
    }, [])

    const handleSetUser = (members: any) => {
        const user = members[0]
        setUserVessels(
            user?.vehicles.nodes.map((vessel: any) => {
                return { label: vessel.title, value: vessel.id }
            }) ?? [],
        )

        const userGroups = user.groups.nodes.map((group: any) => {
            if (group) {
                const groupItem = groups.find((g: any) => g.value === group.id)
                return { label: groupItem.label, value: groupItem.value }
            }
        })[0] // user should only have one group/role

        if (userGroups) {
            setUserGroups(userGroups)
        }

        setUser({ ...user, primaryDutyID: user.primaryDuty?.id })
        const userDepartments = user.departments.nodes.map(
            (department: any) => {
                return department.id
            },
        )
        setSelectedDepartments(userDepartments)

        if (localStorage.getItem('userId') === user?.id) {
            setIsSelf(true)
        }
    }

    getSeaLogsMembers([userId], handleSetUser)

    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const vesselsResponse = activeVessels.map((vessel: any) => {
            return {
                label: vessel.title,
                value: vessel.id,
                vessel: {
                    id: vessel.id,
                    title: vessel.title,
                    icon: vessel.icon,
                    iconMode: vessel.iconMode,
                    photoID: vessel.photoID,
                },
            }
        })
        setVessels(vesselsResponse)
    }

    getVesselList(handleSetVessels)

    getDepartmentList(setDepartmentList)

    const handleSetGroups = (groups: any) => {
        let groupsResponse = groups
            .filter((group: any) => isAdmin() || group.code !== 'admin')
            .map((group: any) => {
                return {
                    label: group.title,
                    value: group.id,
                }
            })
        setGroups(groupsResponse)
    }

    getSeaLogsGroups(handleSetGroups)

    const debouncedhandleInputChange = debounce(
        (name: string, value: string) => {
            setUser({
                ...user,
                [name]: value,
                id: userId,
            })
        },
        300,
    )

    const handleInputChange = (e: any) => {
        const { name, value } = e.target

        debouncedhandleInputChange(name, value)
    }

    const handleGroupChange = (group: any) => {
        const groups = [group]
        setUserGroups(group) // user should only have one group/role
        setUser({
            ...user,
            groups: {
                nodes: groups.map((group: any) => {
                    return { id: group.value }
                }),
            },
        })
    }
    const handleVesselChange = (vessels: any) => {
        const vesselIDs = vessels.map((vessel: any) => {
            return vessel.value
        })
        setUserVessels(vessels)
        setUser({
            ...user,
            vehicles: vesselIDs,
        })
    }
    const handleDutyChange = (duty: any) => {
        setUser({
            ...user,
            primaryDutyID: +duty.value,
        })
    }
    const [mutationCreateUser, { loading: mutationCreateUserLoading }] =
        useMutation(CREATE_USER, {
            onCompleted: (response: any) => {
                const data = response.createSeaLogsMember
                if (data.id > 0) {
                    router.push('/crew')
                } else {
                    console.error('mutationCreateUser response error', response)
                }
            },
            onError: (error: any) => {
                setFormErrors({
                    ...formErrors,
                    response: error.message,
                })
                setHasFormErrors(true)
                console.error('mutationCreateUser catch error', error.message)
            },
        })
    const [mutationUpdateUser, { loading: mutationUpdateUserLoading }] =
        useMutation(UPDATE_USER, {
            onCompleted: (response: any) => {
                const data = response.updateSeaLogsMember
                if (data.id > 0) {
                    seaLogsMemberModel.delete(userId).then(() => {
                        router.push('/crew')
                    })
                } else {
                    console.error('mutationUpdateUser error', response)
                }
            },
            onError: (error: any) => {
                console.error('mutationUpdateUser error', error.message)
                toast.error(error.message)
            },
        })
    const handleSave = async () => {
        if (
            currentDepartment &&
            localStorage.getItem('useDepartment') === 'true'
        ) {
            if (selectedDepartments.length === 0) {
                toast.error('Please select a department')
                return
            }
        }
        if (
            user === undefined ||
            user?.groups === undefined ||
            userGroups.length === 0
        ) {
            toast.error('Please select a role')
            return
        }
        let hasErrors = false
        let errors = {
            firstName: '',
            email: '',
            response: '',
        }
        if (isEmpty(trim(user.firstName))) {
            hasErrors = true
            errors.firstName = 'First name is required'
        }
        if (isEmpty(trim(user.email))) {
            hasErrors = true
            errors.email = 'Email is required'
        }
        if (hasErrors) {
            setHasFormErrors(true)
            setFormErrors(errors)
            return
        }
        const variables = {
            input: {
                id: +user.id,
                firstName: user.firstName,
                surname: user.surname,
                username: user.username,
                password: user.password,
                email: user.email,
                isPilot: user.isPilot,
                isTransferee: user.isTransferee,
                phoneNumber: user.phoneNumber,
                ...(!isEmpty(user.groups) && {
                    groups: user.groups.nodes
                        .map((group: any) => group.id)
                        .join(','),
                }),
                ...(!isEmpty(userVessels) && {
                    vehicles: userVessels
                        .map((vessel: any) => vessel.value)
                        .join(','),
                }),
                primaryDutyID: +user.primaryDutyID,
                departments: selectedDepartments.join(','),
            },
        }
        if (userId === 0) {
            await mutationCreateUser({
                variables,
            })
        } else {
            await mutationUpdateUser({
                variables,
            })
        }
    }

    const handleDepartmentChange = (departments: any) => {
        setSelectedDepartments(departments.map((d: any) => d.value))
    }

    const filterByDepartment = (vessels: any) => {
        return (
            departmentList &&
            departmentList
                ?.filter(
                    (d: any) =>
                        d.seaLogsMembers.nodes?.find(
                            (m: any) => m.id == userId,
                        ) || selectedDepartments.includes(d.id),
                )
                ?.map((d: any) => d.basicComponents.nodes)
                .flat()
                ?.map((v: any) => {
                    return vessels.find((vessel: any) => vessel.value === v.id)
                })
                ?.filter(Boolean) // Remove any undefined entries
        )
    }

    const handleArchiveUser = async (crewInfo: any) => {
        if (crewInfo && crewInfo.id > 0) {
            const variables = {
                input: {
                    id: crewInfo.id,
                    isArchived: !crewInfo.isArchived,
                },
            }
            await mutationUpdateUser({
                variables,
            })
        }
    }

    if (!permissions || !hasPermission('VIEW_MEMBER', permissions)) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <>
            <ListHeader title={`${userId === 0 ? 'Create' : 'Edit'} User`} />
            <Card className="mb-2.5 px-2">
                <CardHeader>
                    <H4>User Details</H4>
                    <P>
                        Personal information and account settings for this user.
                    </P>
                    {hasFormErrors && formErrors.response && (
                        <Alert variant="destructive" className="mb-4">
                            <AlertDescription>
                                {formErrors.response}
                            </AlertDescription>
                        </Alert>
                    )}
                </CardHeader>
                <CardContent className="p-0 space-y-5">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="firstName" label="First name">
                                <Input
                                    id="firstName"
                                    name="firstName"
                                    placeholder="First name"
                                    type="text"
                                    required
                                    defaultValue={user?.firstName || ''}
                                    onChange={handleInputChange}
                                    className="w-full"
                                />
                            </Label>
                            {hasFormErrors && formErrors.firstName && (
                                <p className="text-sm text-destructive">
                                    {formErrors.firstName}
                                </p>
                            )}
                        </div>
                        <Label htmlFor="surname" label="Surname">
                            <Input
                                id="surname"
                                name="surname"
                                placeholder="Surname"
                                type="text"
                                defaultValue={user?.surname || ''}
                                onChange={handleInputChange}
                                className="w-full"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Label htmlFor="username" label="Username">
                            <Input
                                id="username"
                                name="username"
                                placeholder="Username"
                                type="text"
                                defaultValue={user?.username || ''}
                                onChange={handleInputChange}
                                className="w-full"
                            />
                        </Label>
                        {((permissions &&
                            hasPermission('ADMIN', permissions)) ||
                            isSelf) && (
                            <Label htmlFor="password" label="Password">
                                <Input
                                    id="password"
                                    name="password"
                                    placeholder="Password"
                                    type="password"
                                    required={userId === 0}
                                    onChange={handleInputChange}
                                    autoComplete="new-password"
                                    className="w-full"
                                />
                            </Label>
                        )}
                    </div>

                    {((permissions &&
                        hasPermission(
                            process.env.VIEW_MEMBER_CONTACT ||
                                'VIEW_MEMBER_CONTACT',
                            permissions,
                        )) ||
                        isSelf) && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="email" label="Email">
                                    <Input
                                        id="email"
                                        name="email"
                                        type="email"
                                        placeholder="Email"
                                        defaultValue={user?.email || ''}
                                        onChange={handleInputChange}
                                        className="w-full"
                                    />
                                </Label>
                                {hasFormErrors && formErrors.email && (
                                    <p className="text-sm text-destructive">
                                        {formErrors.email}
                                    </p>
                                )}
                            </div>
                            <div>
                                <Label
                                    htmlFor="phoneNumber"
                                    label="Phone Number">
                                    <Input
                                        id="phoneNumber"
                                        name="phoneNumber"
                                        placeholder="Phone Number"
                                        type="tel"
                                        defaultValue={user?.phoneNumber || ''}
                                        onChange={handleInputChange}
                                        className="w-full"
                                    />
                                </Label>
                            </div>
                        </div>
                    )}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Label htmlFor="primaryDuty" label="Primary Duty">
                            <CrewDutyDropdown
                                onChange={handleDutyChange}
                                crewDutyID={user?.primaryDuty?.id || 0}
                            />
                        </Label>

                        <Label htmlFor="userRole" label="User Role">
                            {groups && (
                                <Combobox
                                    placeholder="Select roles"
                                    onChange={handleGroupChange}
                                    value={userGroups}
                                    options={groups}
                                />
                            )}
                        </Label>

                        {localStorage.getItem('useDepartment') === 'true' && (
                            <div>
                                <Label
                                    htmlFor="departments"
                                    label="Departments">
                                    {departmentList && (
                                        <DepartmentMultiSelectDropdown
                                            onChange={handleDepartmentChange}
                                            value={selectedDepartments}
                                            allDepartments={departmentList}
                                        />
                                    )}
                                </Label>

                                <p className="text-xs text-muted-foreground mt-1">
                                    The assigned departments will be cleared if
                                    this user is assigned the Administrator
                                    role.
                                </p>
                            </div>
                        )}
                    </div>
                    <Label htmlFor="vessels" label="Vessels">
                        {vessels && (
                            <Combobox
                                multi
                                onChange={handleVesselChange}
                                placeholder="Select vessels"
                                options={
                                    localStorage.getItem('useDepartment') ===
                                    'true'
                                        ? filterByDepartment(vessels)
                                        : vessels
                                }
                                value={userVessels}
                            />
                        )}
                    </Label>
                </CardContent>
            </Card>
            <AlertDialogNew
                openDialog={openArchiveDialog}
                setOpenDialog={setOpenArchiveDialog}
                noButton={
                    !permissions &&
                    !hasPermission(
                        process.env.DELETE_MEMBER || 'DELETE_MEMBER',
                        permissions,
                    )
                }
                actionText={user?.isArchived ? 'Retrieve' : 'Archive'}
                handleCancel={() => setOpenArchiveDialog(false)}
                cancelText="Cancel"
                variant="warning"
                handleCreate={() => handleArchiveUser(user)}
                title={`${user?.isArchived ? 'Retrieve' : 'Archive'} User`}>
                <div className="flex justify-center flex-col">
                    {permissions &&
                    hasPermission(
                        process.env.DELETE_MEMBER || 'DELETE_MEMBER',
                        permissions,
                    ) ? (
                        <>
                            Are you sure you want to{' '}
                            {user?.isArchived ? 'retrieve' : 'archive'}{' '}
                            {`${user?.firstName || 'this user'} ${user?.surname || ''}`}
                            ?
                        </>
                    ) : (
                        <>
                            <H4 className="text-fire-bush-500">Warning!</H4>
                            <p className="text-fire-bush-900">
                                You do not have permission to archive user.
                            </p>
                        </>
                    )}
                </div>
            </AlertDialogNew>
            <FooterWrapper>
                <Button
                    size="sm"
                    variant="back"
                    onClick={() => router.push('/crew')}>
                    {responsiveLabel('Back', 'Back to users')}
                </Button>
                {permissions &&
                    hasPermission(
                        process.env.EDIT_MEMBER || 'EDIT_MEMBER',
                        permissions,
                    ) && (
                        <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => {
                                setOpenArchiveDialog(true)
                            }}>
                            {user?.isArchived ? 'Retrieve' : 'Archive'}
                        </Button>
                    )}
                <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={
                        mutationCreateUserLoading || mutationUpdateUserLoading
                    }>
                    {userId === 0
                        ? responsiveLabel('Create', 'Create user')
                        : responsiveLabel('Update', 'Update user')}
                </Button>
            </FooterWrapper>
        </>
    )
}

export default UserForm
