"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/maintenance-status-activity/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _export_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./export-button */ \"(app-pages-browser)/./src/app/ui/reporting/export-button.tsx\");\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Column definitions for the DataTable\nconst columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.createColumns)([\n    {\n        accessorKey: \"taskName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                column: column,\n                title: \"Task Name\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 50,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: item.taskName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 54,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"inventoryName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                column: column,\n                title: \"Inventory\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 60,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.inventoryName || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 64,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"vesselName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                column: column,\n                title: \"Location\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 70,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.vesselName || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 74,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"assignedTo\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                column: column,\n                title: \"Assigned To\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 80,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.assignedTo || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 84,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"status\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                column: column,\n                title: \"Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.status || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 94,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"dueDate\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                column: column,\n                title: \"Due Date\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_5___default()(item.dueDate).format(\"DD/MM/YY\") : \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 105,\n                columnNumber: 17\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"dueStatus\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                column: column,\n                title: \"Due Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 114,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: dueStatusLabel(item.dueStatus)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 118,\n                columnNumber: 20\n            }, undefined);\n        }\n    }\n]);\n// Function to evaluate row status for highlighting\nconst getRowStatus = (rowData)=>{\n    var _rowData_dueStatus, _rowData_dueStatus1, _rowData_dueStatus2;\n    if (((_rowData_dueStatus = rowData.dueStatus) === null || _rowData_dueStatus === void 0 ? void 0 : _rowData_dueStatus.status) === \"High\") {\n        return \"overdue\";\n    }\n    if (((_rowData_dueStatus1 = rowData.dueStatus) === null || _rowData_dueStatus1 === void 0 ? void 0 : _rowData_dueStatus1.status) === \"Medium\" || ((_rowData_dueStatus2 = rowData.dueStatus) === null || _rowData_dueStatus2 === void 0 ? void 0 : _rowData_dueStatus2.status) === \"Low\") {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_3__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data || {\n                    startDate: null,\n                    endDate: null\n                });\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    // Handle DataTable's built-in filtering (for client-side search/filter)\n    const handleDataTableChange = (param)=>{\n        let { type, data } = param;\n    // This handles any built-in DataTable filtering\n    // For now, we'll keep it simple since the main filtering is done via the report generation\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_5___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_5___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_5___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                dueStatusLabel(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_8__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_5___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                dueStatusLabel(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_7__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                    variant: \"back\",\n                    iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    onClick: ()=>router.push(\"/reporting\"),\n                    children: \"Back\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 345,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.CardContent, {\n                            className: \"flex flex-col gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_export_button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onDownloadPdf: downloadPdf,\n                                onDownloadCsv: downloadCsv\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.DataTable, {\n                        columns: columns,\n                        data: reportData,\n                        isLoading: called && loading,\n                        onChange: handleFilterOnChange,\n                        rowStatus: getRowStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 356,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"oU/hWC7Aj40C5AHa8r/sGIGstSo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});